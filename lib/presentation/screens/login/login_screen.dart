import 'package:arabic_sign_language/bloc/auth/auth_bloc.dart';
import 'package:arabic_sign_language/bloc/language/language_bloc.dart';
import 'package:arabic_sign_language/bloc/user/user_bloc.dart';
import 'package:arabic_sign_language/bloc/user/user_event.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:arabic_sign_language/presentation/screens/forgot_password/forgot_password_screen.dart';
import 'package:arabic_sign_language/presentation/screens/forgot_password/verification_screen.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/screens/onboard/onboarding_screen.dart';
import 'package:arabic_sign_language/presentation/screens/signup/signup_email_screen.dart';
import 'package:arabic_sign_language/presentation/screens/signup/signup_screen.dart';
import 'package:arabic_sign_language/data/service/google_signin_service.dart';
import 'package:arabic_sign_language/presentation/widgets/custom_gradient_button.dart';
import 'package:arabic_sign_language/presentation/widgets/custom_transparent_button.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LoginScreen extends StatefulWidget {
  final bool sessionExpired;
  const LoginScreen({super.key, this.sessionExpired = false});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  bool _isPasswordVisible = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  // Validation methods
  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  // Method to clear focus and hide keyboard
  void _clearFocus() {
    _emailFocusNode.unfocus();
    _passwordFocusNode.unfocus();
    FocusScope.of(context).unfocus();
  }

  // Step 1: Handle Google Sign-In button tap
  Future<void> _handleGoogleSignIn() async {
    try {
      _clearFocus();

      // Step 1-2: Get ID Token from Google Sign-In + Firebase Auth
      final GoogleSignInService googleSignInService = GoogleSignInService();
      final String? idToken = await googleSignInService.signInWithGoogle();

      if (!mounted) return;

      if (idToken != null) {
        // Step 3: Send ID Token to backend
        context.read<AuthBloc>().add(GoogleSignInRequested(idToken: idToken));
      } else {
        // User canceled sign-in or error occurred
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Google Sign-In was cancelled or failed'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Google Sign-In error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void initState() {
    super.initState();

    // Show session expired message if needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.sessionExpired) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Your session has expired. Please log in again.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 5),
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthLoading) {
            // Show loading indicator
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => const Center(
                child: CircularProgressIndicator(),
              ),
            );
          } else if (state is AuthAuthenticated) {
            // Close loading dialog if open
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }

            context.read<UserBloc>().add(FetchUserProfile());

            // Navigate to home screen or dashboard
            Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (ctx) => const OnboardingScreen()));
          } else if (state is AuthError) {
            // Close loading dialog if open
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }

            _clearFocus();

            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: GestureDetector(
          onTap: _clearFocus,
          child: Stack(
            children: [
              // Background image
              Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(APP_BG),
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              // Foreground content with scroll
              SafeArea(
                child: SingleChildScrollView(
                  reverse: true,
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 75),
                          Center(
                            child: Image.asset(
                              APP_ICON,
                              width: 80,
                              height: 75,
                            ),
                          ),
                          const SizedBox(height: SPACE25),
                          Text(
                            'login'.tr(),
                            style: Theme.of(context).textTheme.displayLarge,
                          ),
                          const SizedBox(height: SPACE15),
                          Text(
                            'enter_email'.tr(),
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          const SizedBox(height: SPACE12),
                          _buildEmailField(),
                          const SizedBox(height: SPACE15),
                          Text(
                            'enter_password'.tr(),
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          const SizedBox(height: SPACE12),
                          _buildPasswordField(),
                          const SizedBox(height: SPACE25),
                          CustomGradientButton(
                            onPressed: () {
                              if (_formKey.currentState!.validate()) {
                                _clearFocus();
                                context.read<AuthBloc>().add(
                                      LoginRequested(
                                        email: _emailController.text.trim(),
                                        password: _passwordController.text,
                                      ),
                                    );
                              }
                            },
                            label: 'login'.tr(),
                            height: 56,
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: () {
                                Navigator.of(context).push(MaterialPageRoute(
                                  builder: (ctx) =>
                                      const ForgotPasswordScreen(),
                                  // VerificationScreen(
                                  //     email: "<EMAIL>"),
                                ));
                              },
                              style: TextButton.styleFrom(
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: Text(
                                'forgot_password'.tr(),
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 13,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ),
                          Center(
                            child: Text(
                              'or_continue_with'.tr(),
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                          const SizedBox(height: SPACE12),
                          Row(
                            children: [
                              Expanded(
                                child: CustomTransparentButton(
                                  onTap: _handleGoogleSignIn,
                                  icon: IC_Google,
                                ),
                              ),
                              const SizedBox(width: SPACE12),
                              Expanded(
                                child: CustomTransparentButton(
                                  onTap: () {},
                                  icon: IC_Apple,
                                ),
                              )
                            ],
                          ),
                          const SizedBox(height: SPACE12),
                          Center(
                            child: CustomTransparentButton(
                              onTap: () {
                                Navigator.pushReplacementNamed(
                                    context, '/home');
                              },
                              label: 'continue_as_guest'.tr(),
                              isWithIcon: false,
                            ),
                          ),
                          const SizedBox(height: SPACE15),
                          Center(
                            child: RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                text: '${'dont_have_account'.tr()}\n',
                                style:
                                    Theme.of(context).textTheme.headlineMedium,
                                children: [
                                  TextSpan(
                                    text: 'create_new_account'.tr(),
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineMedium
                                        ?.copyWith(
                                          decoration: TextDecoration.underline,
                                        ),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () {
                                        _clearFocus();
                                        Navigator.of(context)
                                            .push(MaterialPageRoute(
                                          builder: (ctx) =>
                                              const SignupEmailScreen(),
                                        ));
                                      },
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                  right: 20,
                  top: 50,
                  child: BlocBuilder<LanguageBloc, LanguageState>(
                      builder: (context, state) {
                    return TextButton(
                      onPressed: () {
                        context.read<LanguageBloc>().add(ToggleLanguage());
                        context.setLocale(state.locale.languageCode == 'en'
                            ? const Locale('ar')
                            : const Locale('en'));
                      },
                      child:
                          Text(state.locale.languageCode == 'en' ? 'AR' : 'EN'),
                    );
                  })),
            ],
          ),
        ),
      ),
    );
  }

  // Form field builders
  Widget _buildEmailField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: _emailController.text.isNotEmpty &&
                _validateEmail(_emailController.text) != null
            ? Border.all(color: Colors.red, width: 1)
            : null,
      ),
      child: TextFormField(
        focusNode: _emailFocusNode,
        controller: _emailController,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 16,
        ),
        keyboardType: TextInputType.emailAddress,
        decoration: InputDecoration(
          hintText: 'enter_email'.tr(),
          hintStyle: const TextStyle(color: Colors.grey),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
        validator: _validateEmail,
        textInputAction: TextInputAction.next,
        onChanged: (value) {
          setState(() {}); // Trigger rebuild to update border color
        },
      ),
    );
  }

  Widget _buildPasswordField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: _passwordController.text.isNotEmpty &&
                _validatePassword(_passwordController.text) != null
            ? Border.all(color: Colors.red, width: 1)
            : null,
      ),
      child: TextFormField(
        focusNode: _passwordFocusNode,
        controller: _passwordController,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 16,
        ),
        obscureText: !_isPasswordVisible,
        decoration: InputDecoration(
          hintText: 'enter_password'.tr(),
          hintStyle: const TextStyle(color: Colors.grey),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          suffixIcon: IconButton(
            icon: Icon(
              _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
              color: Colors.grey,
            ),
            onPressed: () {
              setState(() {
                _isPasswordVisible = !_isPasswordVisible;
              });
            },
          ),
        ),
        validator: _validatePassword,
        textInputAction: TextInputAction.done,
        onChanged: (value) {
          setState(() {}); // Trigger rebuild to update border color
        },
      ),
    );
  }
}
