import 'package:arabic_sign_language/data/models/auth/signup_request.dart';
import 'package:arabic_sign_language/data/models/auth/signup_response.dart';
import 'package:arabic_sign_language/presentation/core/url.dart' as URL;
import 'package:arabic_sign_language/presentation/screens/login/login_screen.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../presentation/core/constants.dart';
import '../models/auth/login_request.dart';
import '../models/auth/login_response.dart';
import '../models/auth/email_verification_request.dart';
import '../models/auth/email_verification_response.dart';
import '../models/auth/forgot_password_request.dart';
import '../models/auth/forgot_password_response.dart';
import '../models/auth/verify_code_request.dart';
import '../models/auth/verify_code_response.dart';
import '../models/auth/reset_password_request.dart';
import '../models/auth/reset_password_response.dart';
import '../models/auth/google_signin_request.dart';
import 'package:http/http.dart' as https;




class AuthService {
  // Global navigator key for navigation from anywhere
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  // Dio instances with different content types
  final _loginDio = Dio(BaseOptions(
    headers: {"Content-Type": "application/x-www-form-urlencoded"},
  ));

  final _signupDio = Dio(BaseOptions(
    headers: {"Content-Type": "application/json"},
  ));

  // Dio instance for token refresh
  final _refreshDio = Dio(BaseOptions(
    headers: {"Content-Type": "application/json"},
  ));



  // Singleton pattern
  static final AuthService _instance = AuthService._internal();

  factory AuthService() {
    return _instance;
  }

  AuthService._internal();
  


  // Method to create a Dio instance with auth interceptors while preserving content type
  Dio getDioWithAuth(String baseUrl, {Map<String, dynamic>? headers}) {
    final dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      headers: headers ?? {"Content-Type": "application/json"},
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      followRedirects: true,
      maxRedirects: 5,
      validateStatus: (status) {
        return status != null && status >= 200 && status < 500;
      },
    ));

    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Check if token is expired and refresh if needed
        if (await isTokenExpired()) {
          final refreshed = await refreshToken();
          if (!refreshed) {
            // If refresh failed, logout and redirect to login
            await logout(expired: true);
            return handler.reject(
              DioException(
                requestOptions: options,
                error: 'Session expired',
              ),
            );
          }
        }

        // Add token to request
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString(kEYAccessToken);
        final tokenType = prefs.getString(kEYTokenType) ?? 'Bearer';

        if (token != null) {
          options.headers['Authorization'] = '$tokenType $token';
        }

        return handler.next(options);
      },
      onError: (DioException error, handler) async {
        // Handle 307 redirect
        if (error.response?.statusCode == 307) {
          final redirectUrl = error.response?.headers.value('location');
          if (redirectUrl != null) {
            if (kDebugMode) {
              print("Following redirect to: $redirectUrl");
            }

            final redirectOptions = Options(
              method: error.requestOptions.method,
              headers: error.requestOptions.headers,
              followRedirects: true,
              validateStatus: (status) {
                return status != null && status >= 200 && status < 500;
              },
            );

            final response = await dio.request(
              redirectUrl,
              options: redirectOptions,
              data: error.requestOptions.data,
              queryParameters: error.requestOptions.queryParameters,
            );

            return handler.resolve(response);
          }
        }

        // Handle 401 Unauthorized errors (token expired)
        if (error.response?.statusCode == 401) {
          final refreshed = await refreshToken();
          if (refreshed) {
            // Retry the original request with new token
            final prefs = await SharedPreferences.getInstance();
            final token = prefs.getString(kEYAccessToken);
            final tokenType = prefs.getString(kEYTokenType) ?? 'Bearer';

            error.requestOptions.headers['Authorization'] = '$tokenType $token';

            // Create new request with updated token
            final opts = Options(
              method: error.requestOptions.method,
              headers: error.requestOptions.headers,
              followRedirects: true,
              validateStatus: (status) {
                return status != null && status >= 200 && status < 500;
              },
            );

            final response = await dio.request(
              error.requestOptions.path,
              options: opts,
              data: error.requestOptions.data,
              queryParameters: error.requestOptions.queryParameters,
            );

            return handler.resolve(response);
          } else {
            // If refresh failed, logout and redirect to login
            await logout(expired: true);
          }
        }
        return handler.next(error);
      },
    ));

    return dio;
  }

  Future<LoginResponse> login(String email, String password) async {
    try {
      final request = LoginRequest(email: email, password: password);

      final response = await _loginDio.post(
        URL.LOGIN,
        data: request.toJson(),
      );

      // If login successful, save token expiry time
      if (response.data['status'] == true) {
        final prefs = await SharedPreferences.getInstance();

        // Save tokens
        await prefs.setString(kEYAccessToken, response.data['access_token']);
        await prefs.setString(kEYRefreshToken, response.data['refresh_token']);
        await prefs.setInt(kEYRoleId, response.data['role_id']);
        await prefs.setString(
            kEYTokenType, response.data['token_type'] ?? 'Bearer');

        // Calculate and store token expiry times
        final now = DateTime.now().millisecondsSinceEpoch;

        // Access token expires in 15 mins (3000 seconds)
        final accessTokenExpiry = now + (900 * 1000);
        await prefs.setInt('access_token_expiry', accessTokenExpiry);

        // Refresh token expires in 7 days (604800 seconds)
        final refreshTokenExpiry = now + (604800 * 1000);
        await prefs.setInt('refresh_token_expiry', refreshTokenExpiry);
      }

      return LoginResponse.fromJson(response.data);
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Login error: ${e.message}');
        print('Response data: ${e.response?.data}');
      }

      if (e.response != null) {
        return LoginResponse.fromJson(e.response!.data);
      }

      return LoginResponse(
          status: false,
          detail: "Connection error. Please check your internet connection.");
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error: $e');
      }

      return LoginResponse(
          status: false,
          detail: "An unexpected error occurred. Please try again.");
    }
  }

  Future<bool> refreshToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final refreshToken = prefs.getString(kEYRefreshToken);

      if (refreshToken == null) {
        return false;
      }

      // Check if refresh token is expired
      final refreshTokenExpiry = prefs.getInt('refresh_token_expiry') ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      if (now > refreshTokenExpiry) {
        // Refresh token expired, need to login again
        await logout(expired: true);
        return false;
      }

      final response = await _refreshDio.post(
        '${URL.AUTH_BASE_URL}/api/v1/token/refresh',
        data: {
          'refresh_token': refreshToken,
        },
      );

      if (response.statusCode == 200 && response.data['status']) {
        // Save new tokens
        await prefs.setString(kEYAccessToken, response.data['access_token']);
        await prefs.setString(
            kEYTokenType, response.data['token_type'] ?? 'Bearer');

        // Update expiry times
        final now = DateTime.now().millisecondsSinceEpoch;

        // Access token expires in 1 hour (3600 seconds)
        final accessTokenExpiry = now + (900 * 1000);
        await prefs.setInt('access_token_expiry', accessTokenExpiry);

        return true;
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error refreshing token: $e');
      }
      return false;
    }
  }

  Future<bool> isTokenExpired() async {
    final prefs = await SharedPreferences.getInstance();
    final accessTokenExpiry = prefs.getInt('access_token_expiry') ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;

    // Check if token is expired or about to expire in the next 5 minutes
    return now > (accessTokenExpiry - (5 * 60 * 1000));
  }

  Future<void> logout({bool expired = false}) async {
    final prefs = await SharedPreferences.getInstance();

    // Clear tokens
    await prefs.remove(kEYAccessToken);
    await prefs.remove(kEYRefreshToken);
    await prefs.remove(kEYRoleId);
    await prefs.remove(kEYTokenType);
    await prefs.remove('access_token_expiry');
    await prefs.remove('refresh_token_expiry');

    // Navigate to login screen if context is available
    if (navigatorKey.currentContext != null && expired) {
      // Show session expired message and navigate to login
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        const SnackBar(
          content: Text('Your session has expired. Please log in again.'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 5),
        ),
      );

      // Navigate to login screen (you'll need to implement this navigation)
      Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
        MaterialPageRoute(
            builder: (_) => const LoginScreen(sessionExpired: true)),
        (route) => false,
      );
    }
  }

  Future<SignupResponse> signup(
    String name,
    String email,
    String password,
    String mobile,
    String nationality,
    String verificationCode,
  ) async {
    try {
      final request = SignupRequest(
        name: name,
        email: email,
        password: password,
        mobile: mobile,
        nationality: nationality,
        verificationCode: verificationCode,
      );

      final response = await _signupDio.post(
        URL.SIGNUP,
        data: request.toJson(),
      );

      return SignupResponse.fromJson(response.data);
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Signup error: ${e.message}');
        print('Response data: ${e.response?.data}');
      }

      if (e.response != null) {
        return SignupResponse.fromJson(e.response!.data);
      }

      return SignupResponse(
        status: false,
        detail: "Connection error. Please check your internet connection.",
      );
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error: $e');
      }

      return SignupResponse(
        status: false,
        detail: "An unexpected error occurred. Please try again.",
      );
    }
  }

  Future<EmailVerificationResponse> verifyEmail(String email) async {
    try {
      final request = EmailVerificationRequest(email: email);

      final response = await _signupDio.post(
        URL.EMAIL_VERIFY,
        data: request.toJson(),
      );

      // Both success and failure cases return HTTP 200
      // Parse the response regardless of HTTP status
      return EmailVerificationResponse.fromJson(response.data);
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Email verification error: ${e.message}');
        print('Response data: ${e.response?.data}');
      }

      // If we have response data, try to parse it (could be HTTP 200 with status: false)
      if (e.response != null && e.response!.data != null) {
        try {
          return EmailVerificationResponse.fromJson(e.response!.data);
        } catch (parseError) {
          if (kDebugMode) {
            print('Failed to parse error response: $parseError');
          }
        }
      }

      // Fallback for network errors or unparseable responses
      return EmailVerificationResponse(
        status: false,
        message: "Connection error. Please check your internet connection.",
      );
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error: $e');
      }
      return EmailVerificationResponse(
        status: false,
        message: "An unexpected error occurred. Please try again.",
      );
    }
  }

  Future<ForgotPasswordResponse> forgotPassword(String email) async {
    try {
      final request = ForgotPasswordRequest(email: email);

      final response = await _signupDio.post(
        URL.FORGOT_PASSWORD,
        data: request.toJson(),
      );

      return ForgotPasswordResponse.fromJson(response.data);
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Forgot password error: ${e.message}');
        print('Response data: ${e.response?.data}');
      }

      if (e.response != null) {
        return ForgotPasswordResponse.fromJson(e.response!.data);
      }

      return ForgotPasswordResponse(
        status: false,
        detail: "Connection error. Please check your internet connection.",
      );
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error: $e');
      }

      return ForgotPasswordResponse(
        status: false,
        detail: "An unexpected error occurred. Please try again.",
      );
    }
  }

  Future<VerifyCodeResponse> verifyCode(String email, String code) async {
    try {
      final request = VerifyCodeRequest(email: email, code: code);

      final response = await _signupDio.post(
        URL.VERIFY_CODE,
        data: request.toJson(),
      );

      return VerifyCodeResponse.fromJson(response.data);
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Verification code error: ${e.message}');
        print('Response data: ${e.response?.data}');
      }

      if (e.response != null) {
        return VerifyCodeResponse.fromJson(e.response!.data);
      }

      return VerifyCodeResponse(
        detail: "Connection error. Please check your internet connection.",
      );
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error: $e');
      }

      return VerifyCodeResponse(
        detail: "An unexpected error occurred. Please try again.",
      );
    }
  }

  Future<ResetPasswordResponse> resetPassword(
      String email, String code, String newPassword) async {
    try {
      final request = ResetPasswordRequest(
        email: email,
        code: code,
        newPassword: newPassword,
      );

      final response = await _signupDio.post(
        URL.RESET_PASSWORD,
        data: request.toJson(),
      );

      return ResetPasswordResponse.fromJson(response.data);
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Reset password error: ${e.message}');
        print('Response data: ${e.response?.data}');
      }

      if (e.response != null) {
        return ResetPasswordResponse.fromJson(e.response!.data);
      }

      return ResetPasswordResponse(
        status: false,
        detail: "Connection error. Please check your internet connection.",
      );
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error: $e');
      }

      return ResetPasswordResponse(
        status: false,
        detail: "An unexpected error occurred. Please try again.",
      );
    }
  }

  // Google Sign-In Methods

  /// Step 3: Send ID Token to backend for Google Sign-In
  Future<LoginResponse> googleSignIn(String idToken) async {
    try {
      // Create a fresh Dio instance like your working example
   

      if (kDebugMode) {
        print("=== Google Sign-In Request Debug ===");
        print("URL: ${URL.GOOGLE_SIGNIN}");
        print("Token length: ${idToken.length}");
        print("Token starts with: ${idToken.substring(0, 20)}...");
      }

      // final response = await dio.post(
      //   URL.GOOGLE_SIGNIN,
      //   options: Options(
      //     headers: {
      //       "Content-Type": "application/json",
      //       "Authorization": "Bearer $idToken", // Pass ID token as Bearer token
      //     },
      //   ),
      //   data: {}, // Empty body since token is in header
      // );
         final response = await https.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: {
        'Authorization': 'Bearer $idToken',
        'Content-Type': 'application/json',
      },
        body: {},
      );

      if (kDebugMode) {
        print("=== Google Sign-In Response Debug ===");
        print("Status Code: ${response.statusCode}");
        print("Response Data: ${response.data}");
      }
      // If login successful, save token expiry time
      if (response.data['status'] == true) {
        final prefs = await SharedPreferences.getInstance();

        // Save tokens
        await prefs.setString(kEYAccessToken, response.data['access_token']);
        await prefs.setString(kEYRefreshToken, response.data['refresh_token']);
        await prefs.setInt(kEYRoleId, response.data['role_id']);
        await prefs.setString(
            kEYTokenType, response.data['token_type'] ?? 'Bearer');

        // Calculate and store token expiry times
        final now = DateTime.now().millisecondsSinceEpoch;

        // Access token expires in 15 mins (900 seconds)
        final accessTokenExpiry = now + (900 * 1000);
        await prefs.setInt('access_token_expiry', accessTokenExpiry);

        // Refresh token expires in 7 days (604800 seconds)
        final refreshTokenExpiry = now + (604800 * 1000);
        await prefs.setInt('refresh_token_expiry', refreshTokenExpiry);
      }

      return LoginResponse.fromJson(response.data);
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Google sign-in error: ${e.message}');
        print('Response data: ${e.response?.data}');
        print('Request headers: ${e.requestOptions.headers}');
      }

      if (e.response != null) {
        return LoginResponse.fromJson(e.response!.data);
      }

      return LoginResponse(
        status: false,
        detail: "Connection error. Please check your internet connection.",
      );
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error: $e');
      }

      return LoginResponse(
        status: false,
        detail: "An unexpected error occurred. Please try again.",
      );
    }
  }


}
