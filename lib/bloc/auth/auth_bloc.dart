import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/service/auth_service.dart';

// Events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class LoginRequested extends AuthEvent {
  final String email;
  final String password;

  const LoginRequested({required this.email, required this.password});

  @override
  List<Object> get props => [email, password];
}

class LogoutRequested extends AuthEvent {}

class VerifyEmailRequested extends AuthEvent {
  final String email;

  const VerifyEmailRequested({required this.email});

  @override
  List<Object> get props => [email];
}

class GoogleSignInRequested extends AuthEvent {
  final UserCredential userCredential;

  const GoogleSignInRequested({required this.userCredential});

  @override
  List<Object> get props => [userCredential];
}



// States
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class EmailVerificationLoading extends AuthState {}

class EmailVerificationSuccess extends AuthState {
  final String message;
  final String email;

  const EmailVerificationSuccess({required this.message, required this.email});

  @override
  List<Object> get props => [message, email];
}

class EmailVerificationError extends AuthState {
  final String message;

  const EmailVerificationError({required this.message});

  @override
  List<Object> get props => [message];
}



// Update the AuthAuthenticated state
class AuthAuthenticated extends AuthState {
  final String accessToken;
  final String refreshToken;
  final int roleId;
  final String tokenType;
  final String? successMessage; // Optional success message

  const AuthAuthenticated({
    required this.accessToken,
    required this.refreshToken,
    required this.roleId,
    required this.tokenType,
    this.successMessage,
  });

  @override
  List<Object?> get props => [accessToken, refreshToken, roleId, tokenType, successMessage];
}

class AuthError extends AuthState {
  final String message;

  const AuthError({required this.message});

  @override
  List<Object?> get props => [message];
}

// AuthBloc update
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthService _authService = AuthService();

  AuthBloc() : super(AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
    on<LogoutRequested>(_onLogoutRequested);
    on<VerifyEmailRequested>(_onVerifyEmailRequested);
    on<GoogleSignInRequested>(_onGoogleSignInRequested);
  }

  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final response = await _authService.login(event.email, event.password);

    if (response.isSuccess) {
      // Save tokens to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(kEYAccessToken, response.accessToken!);
      await prefs.setString(kEYRefreshToken, response.refreshToken!);
      await prefs.setInt(kEYRoleId, response.roleId!);
      await prefs.setString(kEYTokenType, response.tokenType!);

      emit(AuthAuthenticated(
        accessToken: response.accessToken!,
        refreshToken: response.refreshToken!,
        roleId: response.roleId!,
        tokenType: response.tokenType!,
        successMessage: "Login successful! Welcome back to Saudi Sign Language.",
      ));
    } else {
      emit(AuthError(message: response.errorMessage));
    }
  }

  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    // Clear tokens from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(kEYAccessToken);
    await prefs.remove(kEYRefreshToken);
    await prefs.remove(kEYRoleId);
    await prefs.remove(kEYTokenType);
    await prefs.remove(kEYNavigationItems);

    emit(AuthInitial());
  }

  Future<void> _onVerifyEmailRequested(
    VerifyEmailRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(EmailVerificationLoading());

    final response = await _authService.verifyEmail(event.email);

    // Both success and failure cases return HTTP 200
    // Check the status field and message content
    if (response.status == true) {
      // New user - verification code sent successfully
      emit(EmailVerificationSuccess(
        message: response.message ?? 'Verification code sent to your email.',
        email: event.email,
      ));
    } else if (response.status == false &&
        response.message == "The email already exists.") {
      // Existing user - show user-friendly error message
      emit(const EmailVerificationError(message: 'User already exists'));
    } else {
      // Any other case - show the actual message or generic error
      emit(EmailVerificationError(
          message: response.message ?? 'Unknown error occurred'));
    }
  }

  Future<void> _onGoogleSignInRequested(
    GoogleSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    // Extract ID token from UserCredential
    final idToken = await event.userCredential.user?.getIdToken();

    if (idToken == null) {
      emit(const AuthError(message: 'Failed to get ID token from Google Sign-In'));
      return;
    }

    final response = await _authService.googleSignIn(idToken);

    if (response.isSuccess) {
      // Save tokens to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(kEYAccessToken, response.accessToken!);
      await prefs.setString(kEYRefreshToken, response.refreshToken!);
      await prefs.setInt(kEYRoleId, response.roleId!);
      await prefs.setString(kEYTokenType, response.tokenType!);

      emit(AuthAuthenticated(
        accessToken: response.accessToken!,
        refreshToken: response.refreshToken!,
        roleId: response.roleId!,
        tokenType: response.tokenType!,
        successMessage: "Google Sign-In successful! Welcome to Saudi Sign Language.",
      ));
    } else {
      emit(AuthError(message: response.errorMessage));
    }
  }


}
